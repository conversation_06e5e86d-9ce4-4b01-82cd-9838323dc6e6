import logging

from selenium.webdriver.support.wait import Web<PERSON><PERSON><PERSON>ait

from utils import DriverUtils


# 基类：存放二次封装的代码
class BasePage:

    def __init__(self):
        self.driver=DriverUtils.get_driver()

    # 公用元素定位
    def find_el(self,location):

        try:
            el = WebDriverWait(DriverUtils.get_driver(),10,1).\
             until(lambda x:x.find_element(*location))
            logging.info(f"find {location} success!")
        except Exception as e:
            el = None
            logging.error(f"find {location} failed!")
        return el

    def input_text(self,element,text):
        try:
            element.clear()
            element.send_keys(text)
            logging.info(f"input {text} success!")
        except Exception as e:
            logging.error(f"input {text} failed! Error: {e}")

    # frame切换
    def switch_frame(self,i_el):
        try:
            self.driver.switch_to.frame(i_el)
            logging.info(f"switch frame success!")
        except Exception as e:
            logging.error(f"switch frame failed!")

    #窗口切换
    def switch_window(self,n):
        try:
            self.driver.switch_to.window(self.driver.window_handles[n])
            logging.info(f"switch window success!")
        except Exception as e:
            logging.error(f"switch window failed!")