# 调试页面加载问题的简单脚本
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def test_page_loading():
    print("🚀 开始测试页面加载...")
    
    # 创建浏览器驱动
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service)
        driver.maximize_window()
        driver.implicitly_wait(10)
        print("✅ 浏览器启动成功")
    except Exception as e:
        print(f"❌ 浏览器启动失败: {e}")
        return
    
    try:
        print("🔄 正在加载页面: http://hmshop-test.itheima.net/")
        driver.get("http://hmshop-test.itheima.net/")
        
        # 等待页面加载
        time.sleep(5)
        
        print(f"📍 当前URL: {driver.current_url}")
        print(f"📄 页面标题: '{driver.title}'")
        print(f"📏 页面源码长度: {len(driver.page_source)} 字符")
        
        # 检查页面加载状态
        if driver.current_url == "data:," or driver.current_url == "about:blank":
            print("❌ 页面加载失败！显示空白页面")
            print("可能的原因：")
            print("   1. 网站 http://hmshop-test.itheima.net/ 无法访问")
            print("   2. 网络连接问题")
            print("   3. DNS解析失败")
            print("   4. 防火墙阻止访问")
        elif not driver.title or driver.title.strip() == "":
            print("⚠️ 页面部分加载，但标题为空")
            print(f"页面源码前500字符: {driver.page_source[:500]}")
        else:
            print("✅ 页面加载成功！")
            print("尝试查找登录元素...")
            
            # 尝试查找登录元素
            try:
                login_elements = driver.find_elements("xpath", "//a[contains(text(),'登录')]")
                if login_elements:
                    print(f"✅ 找到 {len(login_elements)} 个包含'登录'的链接元素")
                else:
                    print("❌ 未找到包含'登录'的链接元素")
                    
                    # 尝试其他可能的登录元素
                    alt_login_elements = driver.find_elements("xpath", "//*[text()='登录']")
                    if alt_login_elements:
                        print(f"✅ 找到 {len(alt_login_elements)} 个文本为'登录'的元素")
                    else:
                        print("❌ 也未找到文本为'登录'的元素")
                        print("页面可能结构不同，或者登录元素使用了不同的文本")
                        
            except Exception as e:
                print(f"❌ 查找登录元素时出错: {e}")
        
    except Exception as e:
        print(f"❌ 页面加载过程中出错: {e}")
    
    finally:
        print("🔚 关闭浏览器")
        driver.quit()

if __name__ == "__main__":
    test_page_loading()
