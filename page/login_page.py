# 定义页面对象
from selenium.webdriver.common.by import By

from base.base_page import BasePage
from utils import DriverUtils


class LoginPage(BasePage):

    # 定义页面实例属性：页面上的元素信息
    def __init__(self):
        # 驱动对象
        self.driver = DriverUtils.get_driver()
        # 用户名输入框
        self.username = (By.ID, "username")
        # 密码输入框
        self.password = (By.ID, "password")
        # 验证码输入框
        self.verify_code = (By.ID, 'verify_code')
        # 登录按钮
        self.login_btn = (By.XPATH, "//*[@class='login_bnt']/a")
        # 定义哪些元素：测试工程师所要测试的内容用到该页面哪些元素就定义哪些元素


# 定义页面业务方法：组装该页面上的一些操作形成操作步骤
    def tp_login(self, username, password, verify_code):
        """

        :param username: 用户名
        :param password: 密码
        :param verify_code: 验证码
        :return:
        """


        # 1.输入用户名
        self.input_text(self.find_el(self.username),username)
        # 2.输入密码
        self.input_text(self.find_el(self.password),password)
        # 3.输入验证码
        self.input_text(self.find_el(self.verify_code),verify_code)
        # 4.点击登录按钮
        self.find_el(self.login_btn).click()

        # # 清除用户名输入框
        # self.driver.find_element(*self.username).clear()
        # # 1.输入用户名
        # self.driver.find_element(*self.username).send_keys(username)
        # # 清除密码输入框
        # self.driver.find_element(*self.password).clear()
        # # 2.输入密码
        # self.driver.find_element(*self.password).send_keys(password)
        # # 清除验证码输入框
        # self.driver.find_element(*self.verify_code).clear()
        # # 3.输入验证码
        # self.driver.find_element(*self.verify_code).send_keys(verify_code)
        # # 4.点击登录按钮
        # self.driver.find_element(*self.login_btn).click()