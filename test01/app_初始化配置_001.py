"""
启动模拟器中设置应用
"""

# ① 导包
import time
from appium import webdriver

# ② 配置启动参数
desied_caps = {}
desied_caps["platformName"] = "Android"  # IOS
desied_caps["platformVersion"] = "9"
desied_caps["deviceName"] = "adassd"  # 可以随便写但是不能不写
desied_caps["appPackage"] = "com.android.settings"  # 包名
desied_caps["appActivity"] = "com.android.settings.Settings"  # 界面名

# ③ 创建APP驱动对象
driver = webdriver.Remote('http://127.0.0.1:4723/wd/hub', desied_caps)
# ④ 业务操作
time.sleep(5)
# ⑤ 关闭APP
driver.quit()
