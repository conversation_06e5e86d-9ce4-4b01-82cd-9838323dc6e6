# 1.导包
import time

from selenium import webdriver
# 2.打开浏览器 - 创建浏览器驱动对象
from selenium.webdriver.common.by import By

from test01.utils import get_el_text
from utils import DriverUtils


class TestLogin:

    # 开始执行测试用例之前只会打开一次浏览器
    def setup_class(self):
        self.driver = webdriver.Chrome()
        self.driver.maximize_window()
        self.driver.implicitly_wait(30)
        self.driver.get("http://hmshop-test.itheima.net/")

    # 所有测试都执行完毕后才会关闭浏览器
    def teardown_class(self):
        time.sleep(2)
        self.driver.quit()

    # 每个测试方法的起点一致，那么证明每个测试方法运行之前都会回到首页
    def setup(self):
        print("🟢 setup 开始执行...")
        print(f"   当前 URL: {self.driver.current_url}")
        self.driver.get("http://hmshop-test.itheima.net/")
        print(f"   执行后 URL: {self.driver.current_url}")

    def test_login_account_not_exist(self):
        # 4.暂停3s->代替测试步骤
        # a. 使用 Xpath 文本定位策略定位登陆超链接，并点击；
        self.driver.find_element(By.XPATH, "//*[text()='登录']").click()
        # b. 使用 Xpath 属性定位策略定位用户名输入框，输入：***********；
        self.driver.find_element(By.XPATH, "//*[@id='username']").send_keys("123")
        # c. 使用 Xpath 属性包含定位策略定位密码输入框，输入：123456；
        self.driver.find_element(By.XPATH, "//*[contains(@id,'pass')]").send_keys("123456")
        # d. 使用 Xpath 属性与逻辑结合策略定位验证码输入框，输入：8888；
        self.driver.find_element(By.XPATH, '//*[@placeholder="验证码" and @id="verify_code"]').send_keys("8888")
        # e. 使用 Xpath 层级与属性结合策略定位登陆按钮，并点击；
        self.driver.find_element(By.XPATH, "//*[@class='login_bnt']/a").click()
        time.sleep(2)
        # 获取实际结果
        msg = get_el_text("//div[@class='layui-layer-content layui-layer-padding']")
        print(msg)
        # 判断实际结果和预期结果是否一致
        assert "账号不存在" in msg
        self.driver.find_element(By.XPATH,"//a[contains(text(),'确定')]").click()


    def test_login_password_error(self):
        # 4.暂停3s->代替测试步骤
        # a. 使用 Xpath 文本定位策略定位登陆超链接，并点击；
        self.driver.find_element(By.XPATH, "//*[text()='登录']").click()
        # b. 使用 Xpath 属性定位策略定位用户名输入框，输入：15617658555；
        self.driver.find_element(By.XPATH, "//*[@id='username']").send_keys("15617658555")
        # c. 使用 Xpath 属性包含定位策略定位密码输入框，输入：error；
        self.driver.find_element(By.XPATH, "//*[contains(@id,'pass')]").send_keys("error")
        # d. 使用 Xpath 属性与逻辑结合策略定位验证码输入框，输入：8888；
        self.driver.find_element(By.XPATH, '//*[@placeholder="验证码" and @id="verify_code"]').send_keys("8888")
        # e. 使用 Xpath 层级与属性结合策略定位登陆按钮，并点击；
        self.driver.find_element(By.XPATH, "//*[@class='login_bnt']/a").click()
        time.sleep(2)
        # 获取实际结果
        msg = get_el_text("//div[@class='layui-layer-content layui-layer-padding']")
        print(msg)
        assert "密码错误" in msg
