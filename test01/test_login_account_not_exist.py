# 1.导包
import time

from selenium import webdriver
# 2.打开浏览器 - 创建浏览器驱动对象
from selenium.webdriver.common.by import By

driver = webdriver.Chrome()
driver.maximize_window()
driver.implicitly_wait(30)

# 3.打开测试网址:Tpshop首页
driver.get("http://hmshop-test.itheima.net/")

# 4.暂停3s->代替测试步骤
# a. 使用 Xpath 文本定位策略定位登陆超链接，并点击；
driver.find_element(By.XPATH, "//*[text()='登录']").click()
# b. 使用 Xpath 属性定位策略定位用户名输入框，输入：15800000001；
driver.find_element(By.XPATH, "//*[@id='username']").send_keys("15801010202")
# c. 使用 Xpath 属性包含定位策略定位密码输入框，输入：123456；
driver.find_element(By.XPATH, "//*[contains(@id,'pass')]").send_keys("123456")
# d. 使用 Xpath 属性与逻辑结合策略定位验证码输入框，输入：8888；
driver.find_element(By.XPATH, '//*[@placeholder="验证码" and @id="verify_code"]').send_keys("8888")
# e. 使用 Xpath 层级与属性结合策略定位登陆按钮，并点击；
driver.find_element(By.XPATH, "//*[@class='login_bnt']/a").click()
time.sleep(2)
# 获取实际结果
msg = driver.find_element(By.CSS_SELECTOR, ".layui-layer-padding").text
print(msg)
# 关闭浏览器
time.sleep(2)
driver.quit()
