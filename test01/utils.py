import logging
import time

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait


class DriverUtils:

    # 私有属性
    __driver=None

    @classmethod
    def get_driver(cls):

        if cls.__driver == None:
            cls.__driver = webdriver.Chrome()
            cls.__driver.maximize_window()
            cls.__driver.implicitly_wait(30)
            return cls.__driver
    @classmethod
    def quit_driver(cls):
        if cls.__driver is not None:
            time.sleep(2)
            cls.__driver.quit()
            cls.__driver = None

# 函数：公用的获取任意元素文本
def get_el_text(xpath_str):
    # 获取元素文本
    # msg = DriverUtils.get_driver().find_element(By.CSS_SELECTOR, xpath_str).text
    try:
        msg = WebDriverWait(DriverUtils.get_driver(),10,1).\
               until(lambda x:x.find_element(By.CSS_SELECTOR, xpath_str)).text
    except Exception as e:
        logging.error(f"没有获取到{xpath_str}的元素对象的文本！")
        msg = None
    return msg


def el_is_exist_by_text(key_text):
    try:
        is_suc = (WebDriverWait(DriverUtils.get_driver(), 10, 0.5).
                  until(lambda x: x.find_element(By.XPATH, f"//*[text()='{key_text}']")))
    except Exception as e:
        is_suc = False
        DriverUtils.get_driver().get_screenshot_as_file(f"{key_text}未找到.png")
        logging.error(f"未找到文本为{key_text}的元素对象！")
    return is_suc


DriverUtils.get_driver()
DriverUtils.quit_driver()
DriverUtils.get_driver()