# 测试替代网站的脚本
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def test_multiple_sites():
    print("🚀 测试多个网站的可访问性...")
    
    # 测试网站列表
    test_sites = [
        "https://www.baidu.com",  # 百度 - 测试基本网络连接
        "https://www.example.com",  # 示例网站
        "http://hmshop-test.itheima.net/",  # 原始测试网站
        "https://demo.opencart.com/",  # 开源电商演示网站
        "https://automationexercise.com/",  # 自动化测试练习网站
    ]
    
    # 创建浏览器驱动
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service)
        driver.maximize_window()
        driver.implicitly_wait(5)
        print("✅ 浏览器启动成功")
    except Exception as e:
        print(f"❌ 浏览器启动失败: {e}")
        return
    
    for i, site in enumerate(test_sites, 1):
        print(f"\n🔄 测试网站 {i}: {site}")
        try:
            driver.get(site)
            time.sleep(3)  # 等待页面加载
            
            print(f"   📍 当前URL: {driver.current_url}")
            print(f"   📄 页面标题: '{driver.title}'")
            
            if driver.current_url == "data:," or driver.current_url == "about:blank":
                print("   ❌ 页面加载失败")
            elif driver.title and driver.title.strip():
                print("   ✅ 页面加载成功")
                
                # 如果是电商网站，尝试查找登录相关元素
                if "opencart" in site or "automationexercise" in site:
                    try:
                        login_elements = driver.find_elements("xpath", "//*[contains(text(),'Login') or contains(text(),'登录') or contains(text(),'Sign in')]")
                        if login_elements:
                            print(f"   🔍 找到 {len(login_elements)} 个登录相关元素")
                        else:
                            print("   🔍 未找到明显的登录元素")
                    except:
                        pass
            else:
                print("   ⚠️ 页面部分加载")
                
        except Exception as e:
            print(f"   ❌ 访问失败: {e}")
    
    print("\n🔚 关闭浏览器")
    driver.quit()

if __name__ == "__main__":
    test_multiple_sites()
