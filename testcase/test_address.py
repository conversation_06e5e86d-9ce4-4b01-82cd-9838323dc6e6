import time

from selenium.webdriver import Action<PERSON>hains
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support.select import Select

from page.login_page import LoginPage
from utils import DriverUtils, el_is_exist_by_text


class TestAddress:
    # 开始执行测试用例之前只会打开一次浏览器
    def setup_class(self):
        self.driver=DriverUtils.get_driver()

    # 所有测试都执行完毕后才会关闭浏览器
    def teardown_class(self):
        DriverUtils.quit_driver()

    def test_01_login_suc(self):
        # 4.暂停3s->代替测试步骤
        # a. 使用 Xpath 文本定位策略定位登陆超链接，并点击；
        self.driver.find_element(By.XPATH, "//*[text()='登录']").click()
        LoginPage().tp_login("15617658555","123456","8888")

        time.sleep(2)

    def test_02_address_suc(self):
        ActionChains(self.driver).move_to_element(self.driver.find_element(By.XPATH,"//span[contains(text(),'账户设置')]")).perform()
        self.driver.find_element(By.XPATH,"//a[text()='收货地址']").click()
        old_num=self.driver.find_elements(By.CSS_SELECTOR,"em.red")[0].text
        print(f"新增地址前，已保存的地址条数为{old_num}")
        self.driver.find_element(By.XPATH,"//span[@class='co_blue']").click()
        customer_name=f"cus{time.strftime('%H%M%S')}"
        self.driver.find_element(By.XPATH,"//input[@name='consignee']").send_keys(customer_name)
        self.driver.find_element(By.XPATH,"//input[@placeholder='固话号码格式:xx-xx(例如: 0755-86140485)']").send_keys("15617658555")
        Select(self.driver.find_element(By.XPATH,"//select[@id='province']")).select_by_value("1")
        Select(self.driver.find_element(By.XPATH,"//select[@id='city']")).select_by_value("2")
        Select(self.driver.find_element(By.XPATH,"//select[@id='district']")).select_by_value("3")
        self.driver.find_element(By.XPATH,"//input[@placeholder='详细地址']").send_keys("测试地址")
        self.driver.find_element(By.XPATH,"//input[@name='zipcode']").send_keys("123456")
        self.driver.find_element(By.XPATH,"//button[@id='address_submit']").click()
        time.sleep(1)
        #
        # try:
        #     is_suc=(WebDriverWait(self.driver,10,0.5).
        #             until(lambda x:x.find_element(By.XPATH,f"//*[text()='{customer_name}']")))
        # except Exception as e:
        #     is_suc=False
        #     self.driver.get_screenshot_as_file("新增地址失败.png")
        assert el_is_exist_by_text(customer_name)





