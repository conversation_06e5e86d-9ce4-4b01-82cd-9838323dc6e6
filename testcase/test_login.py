# 1.导包
import time
from selenium.webdriver.common.by import By

from page.login_page import LoginPage
from utils import DriverUtils, get_el_text


# 1.定义测试类 --->模块
class TestLogin:

    # 开始执行测试之前只会打开一次浏览器
    def setup_class(self):
        self.driver = DriverUtils.get_driver()

    # 所有的测试用例都运行完毕才会关闭浏览器
    def teardown_class(self):
        # 关闭浏览器
        DriverUtils.quit_driver()

    # 每个测试方法的起点一次,那么证明每个测试方法运行之前都回到首页
    def setup(self):
        self.driver.get("http://hmshop-test.itheima.net/")

    # 2.定义测试方法 --->标题
    # 登录失败-(账户不存在)
    def test_login_account_not_exist(self):
        # 4.暂停3s->代替测试步骤
        # a. 使用 Xpath 文本定位策略定位登陆超链接，并点击；
        self.driver.find_element(By.XPATH, "//a[contains(text(),'登录')]").click()
        LoginPage().tp_login("***********", "123456", "8888")
        # # b. 使用 Xpath 属性定位策略定位用户名输入框，输入：***********；
        # self.driver.find_element(By.XPATH, "//*[@id='username']").send_keys("***********")
        # # c. 使用 Xpath 属性包含定位策略定位密码输入框，输入：123456；
        # self.driver.find_element(By.XPATH, "//*[contains(@id,'pass')]").send_keys("123456")
        # # d. 使用 Xpath 属性与逻辑结合策略定位验证码输入框，输入：8888；
        # self.driver.find_element(By.XPATH, '//*[@placeholder="验证码" and @id="verify_code"]').send_keys("8888")
        # # e. 使用 Xpath 层级与属性结合策略定位登陆按钮，并点击；
        # self.driver.find_element(By.XPATH, "//*[@class='login_bnt']/a").click()
        time.sleep(2)
        # 获取实际结果
        msg = get_el_text("//*[@class='layui-layer-content layui-layer-padding']")
        # 判断实际结果和预期结果是否一致
        assert "账号不存在" in msg

    # 登录失败-(密码错误)
    def test_login_password_error(self):
        # 4.暂停3s->代替测试步骤
        # a. 使用 Xpath 文本定位策略定位登陆超链接，并点击；
        DriverUtils().get_driver().find_element(By.XPATH, "//*[text()='登录']").click()
        LoginPage().tp_login("***********", "123456", "8888")
        # # b. 使用 Xpath 属性定位策略定位用户名输入框，输入：***********；
        # self.driver.find_element(By.XPATH, "//*[@id='username']").send_keys("***********")
        # # c. 使用 Xpath 属性包含定位策略定位密码输入框，输入：123456；
        # self.driver.find_element(By.XPATH, "//*[contains(@id,'pass')]").send_keys("error")
        # # d. 使用 Xpath 属性与逻辑结合策略定位验证码输入框，输入：8888；
        # self.driver.find_element(By.XPATH, '//*[@placeholder="验证码" and @id="verify_code"]').send_keys("8888")
        # # e. 使用 Xpath 层级与属性结合策略定位登陆按钮，并点击；
        # self.driver.find_element(By.XPATH, "//*[@class='login_bnt']/a").click()
        time.sleep(2)
        # 获取实际结果
        msg = get_el_text("//*[@class='layui-layer-content layui-layer-padding']")
        # 期望的提示信息包含在实际结果中
        assert "密码错误" in msg
