# 驱动工具类
import logging
import time

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait
from webdriver_manager.chrome import ChromeDriverManager


class DriverUtils:
    # 私有属性
    __driver = None
    # 获取浏览器驱动对象
    @classmethod
    def get_driver(cls):
        if cls.__driver is None:
            service = Service(ChromeDriverManager().install())
            cls.__driver = webdriver.Chrome(service=service)
            cls.__driver.maximize_window()
            cls.__driver.implicitly_wait(10)
        return cls.__driver

    @classmethod
    # 关闭浏览器驱动对象
    def quit_driver(cls):
        # 为了增强代码的健壮性，避免单独调用关闭浏览器驱动方法时会报错，在调用关闭驱动对象的方法时先判断当前是否有打开的浏览器
        if cls.__driver is not None:
            time.sleep(3)
            cls.__driver.quit()
            cls.__driver = None

# 公共的获取任意元素文本
def get_el_text(xpath_str):
    # 获取文本元素
    # msg = DriverUtils.get_driver().find_element(By.XPATH, xpath_str).text
    try:
        msg = WebDriverWait(DriverUtils.get_driver(), 10, 0.5).\
            until(lambda x: x.find_element(By.XPATH, xpath_str)).text
        print(msg)
    except Exception as e:
        logging.error(f"没有获取到{xpath_str}的元素对象文本！")
        msg = None
    # 返回获取的文本
    return msg

# 函数：根据文本判断当前页面是否有对应的元素对象
def el_is_exist_by_text(key_text):
    try:
        # 如果找到元素对象则把元素对象赋值给is_suc
        is_suc = WebDriverWait(DriverUtils.get_driver(),10,0.5).\
            until(lambda x:x.find_element(By.XPATH,f"//*[text()='{key_text}']"))
    except Exception as e:
        # 如果没有找到元素对象则把False赋值给is_suc
        is_suc = False
        # 截图
        DriverUtils.get_driver().get_screenshot_as_file(f"../image/{key_text}未找到.png")
        logging.error(f"未找到文本为{key_text}的元素对象！")
    return is_suc

